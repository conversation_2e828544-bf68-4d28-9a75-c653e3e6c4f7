# Deployment Guide

## Pre-deployment Checklist

1. **AWS Credentials**: Ensure AWS CLI is configured with appropriate permissions
2. **Terraform**: Version 1.0 or higher installed
3. **Variables**: Copy `terraform.tfvars.example` to `terraform.tfvars` and customize

## Required AWS Permissions

Your AWS credentials need the following permissions:
- EC2 (instances, security groups, key pairs)
- VPC (VPC, subnets, route tables, internet gateways, NAT gateways)
- RDS (instances, subnet groups, parameter groups)
- ELB (load balancers, target groups, listeners)
- API Gateway (REST APIs, deployments, stages, VPC links)
- IAM (roles, policies for RDS monitoring)
- CloudWatch (log groups)

## Step-by-Step Deployment

### 1. Configure Variables

```bash
cp terraform.tfvars.example terraform.tfvars
```

Edit `terraform.tfvars` with your values:

```hcl
# Required
db_password = "your-secure-password-123!"

# Optional customizations
aws_region = "us-west-2"
environment = "dev"
project_name = "virtual-chef-ai"
key_pair_name = "your-key-pair"  # For SSH access
```

### 2. Initialize and Deploy

```bash
# Initialize Terraform
terraform init

# Review the plan
terraform plan

# Deploy infrastructure
terraform apply
```

### 3. Verify Deployment

After successful deployment, check the outputs:

```bash
terraform output
```

You should see:
- VPC and subnet IDs
- EC2 instance information
- Load balancer DNS name
- API Gateway URL
- RDS endpoint (sensitive)

### 4. Test the Infrastructure

1. **Test Load Balancer**:
   ```bash
   curl http://$(terraform output -raw nlb_dns_name)
   ```

2. **Test API Gateway**:
   ```bash
   curl $(terraform output -raw api_gateway_url)
   ```

3. **SSH to EC2** (if key pair configured):
   ```bash
   # SSH to public instance
   ssh -i your-key.pem ubuntu@$(terraform output -raw ec2_public_ip)

   # SSH to private instance (through public instance as bastion)
   ssh -i your-key.pem -J ubuntu@$(terraform output -raw ec2_public_ip) ubuntu@$(terraform output -raw ec2_private_instance_private_ip)
   ```

## Post-Deployment Configuration

### Database Connection

The RDS instance is accessible only from EC2 instances. Connection details:
- **Endpoint**: `terraform output rds_endpoint`
- **Port**: `3306`
- **Database**: `virtualchefai`
- **Username**: `admin`
- **Password**: (as configured in terraform.tfvars)

### Application Deployment

1. **Deploy to EC2**: Use the public instance for application deployment or the private instance for secure backend services
2. **Database Setup**: Connect to RDS from EC2 instances to set up schemas
3. **Load Balancer**: Configure your application to listen on port 8080
4. **API Gateway**: Routes traffic through VPC Link to your application

### Ubuntu 24.04 LTS Instances

Both instances come pre-configured with:
- **Apache2 web server** (accessible on port 80)
- **Docker and Docker Compose** for containerized applications
- **AWS CLI v2** for AWS service interactions
- **Node.js LTS** for JavaScript applications
- **Python 3 with pip** for Python applications
- **Development tools** (git, vim, htop, jq, etc.)

**Public Instance**:
- Has internet access and public IP
- Suitable for web servers, load balancers, bastion hosts
- Can be used as a jump host to access private instance

**Private Instance**:
- No direct internet access (uses NAT Gateway for outbound)
- Suitable for application servers, databases, internal services
- More secure for sensitive workloads

## Monitoring and Maintenance

### CloudWatch Logs
- API Gateway logs: `/aws/apigateway/`
- RDS logs: Available in RDS console

### RDS Monitoring
- Enhanced monitoring enabled with 60-second intervals
- Performance Insights enabled with 7-day retention

### Security Groups
- EC2: Allows HTTP (80), HTTPS (443), SSH (22), and app port (8080)
- RDS: Allows MySQL (3306) and PostgreSQL (5432) from EC2 security group

## Troubleshooting

### Common Issues

1. **VPC Link Creation Fails**: Ensure NLB is fully provisioned before API Gateway
2. **RDS Connection Issues**: Check security groups and subnet routing
3. **EC2 SSH Issues**: Verify key pair name and security group rules

### Useful Commands

```bash
# Check resource status
terraform state list

# Get specific output
terraform output nlb_dns_name

# Refresh state
terraform refresh

# Import existing resources (if needed)
terraform import aws_instance.example i-1234567890abcdef0
```

## Cleanup

To destroy all resources:

```bash
terraform destroy
```

**Warning**: This will permanently delete all resources including the database. Ensure you have backups if needed.

## Cost Estimation

Default configuration (us-west-2):
- EC2 t3.micro instances: ~$8.50/month each
- RDS db.t3.micro: ~$12.60/month
- NAT Gateway: ~$32.40/month each
- Network Load Balancer: ~$16.20/month
- API Gateway: Pay per request

Total estimated cost: ~$70-100/month for development environment.

## Next Steps

1. **SSL/TLS**: Add SSL certificates to the load balancer
2. **Auto Scaling**: Implement Auto Scaling Groups for EC2
3. **Backup**: Configure automated RDS backups
4. **Monitoring**: Set up CloudWatch alarms
5. **Security**: Implement WAF for API Gateway
6. **CI/CD**: Integrate with deployment pipelines
