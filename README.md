# Virtual Chef AI Infrastructure

This Terraform project creates a complete AWS infrastructure for the Virtual Chef AI application with the following components:

## Architecture

- **VPC**: Virtual Private Cloud with public and private subnets across multiple AZs
- **EC2**: Auto-scaling EC2 instances in both public and private subnets
- **RDS**: MySQL database in private subnets with enhanced monitoring
- **NLB**: Network Load Balancer for high-performance traffic distribution
- **API Gateway**: REST API Gateway with VPC Link integration

## Project Structure

```
├── main.tf                 # Main configuration
├── variables.tf            # Input variables
├── outputs.tf              # Output values
├── terraform.tfvars.example # Example variables file
└── modules/
    ├── vpc/                # VPC module
    ├── ec2/                # EC2 module
    ├── rds/                # RDS module
    ├── nlb/                # Network Load Balancer module
    └── api-gateway/        # API Gateway module
```

## Prerequisites

1. **AWS CLI** configured with appropriate credentials
2. **Terraform** >= 1.0 installed
3. **AWS Key Pair** (optional, for SSH access to EC2 instances)

## Quick Start

1. **<PERSON>lone and navigate to the project:**
   ```bash
   cd infra-virtual-chef-ai-terraform
   ```

2. **Copy and customize the variables file:**
   ```bash
   cp terraform.tfvars.example terraform.tfvars
   ```
   
   Edit `terraform.tfvars` with your specific values:
   - Set your AWS region
   - Configure database password
   - Set your key pair name (if needed)

3. **Initialize Terraform:**
   ```bash
   terraform init
   ```

4. **Plan the deployment:**
   ```bash
   terraform plan
   ```

5. **Apply the configuration:**
   ```bash
   terraform apply
   ```

6. **Get the outputs:**
   ```bash
   terraform output
   ```

## Configuration

### Required Variables

- `db_password`: Database password (sensitive)

### Optional Variables

- `aws_region`: AWS region (default: us-west-2)
- `environment`: Environment name (default: dev)
- `project_name`: Project name (default: virtual-chef-ai)
- `instance_type`: EC2 instance type (default: t3.micro)
- `key_pair_name`: AWS key pair for SSH access (default: empty)

## Outputs

After deployment, you'll get:

- **VPC ID and subnet IDs**
- **EC2 instance IDs and IP addresses**
- **RDS endpoint** (sensitive)
- **Load Balancer DNS name**
- **API Gateway URL**

## Security Features

- RDS in private subnets only
- Security groups with minimal required access
- Encrypted RDS storage
- VPC Link for secure API Gateway integration

## Monitoring

- RDS Enhanced Monitoring enabled
- CloudWatch logs for API Gateway
- Performance Insights for RDS

## Cleanup

To destroy all resources:

```bash
terraform destroy
```

## Cost Optimization

The default configuration uses:
- t3.micro EC2 instances (free tier eligible)
- db.t3.micro RDS instance (free tier eligible)
- Minimal storage allocations

For production, consider:
- Larger instance types
- Multi-AZ RDS deployment
- Auto Scaling Groups
- Application Load Balancer with SSL certificates

## Support

This infrastructure follows AWS best practices and is suitable for development and testing environments. For production use, additional considerations for high availability, backup strategies, and security hardening should be implemented.
