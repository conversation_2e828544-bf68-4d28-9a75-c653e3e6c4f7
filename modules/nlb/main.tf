# Network Load Balancer
resource "aws_lb" "main" {
  name               = "${var.project_name}-${var.environment}-nlb"
  internal           = false
  load_balancer_type = "network"
  subnets            = var.public_subnet_ids

  enable_deletion_protection = false

  tags = {
    Name = "${var.project_name}-${var.environment}-nlb"
  }
}

# Target Group for HTTP traffic
resource "aws_lb_target_group" "http" {
  name     = "${var.project_name}-${var.environment}-http-tg"
  port     = 80
  protocol = "TCP"
  vpc_id   = var.vpc_id

  health_check {
    enabled             = true
    healthy_threshold   = 2
    interval            = 30
    matcher             = "200"
    path                = "/"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 6
    unhealthy_threshold = 2
  }

  tags = {
    Name = "${var.project_name}-${var.environment}-http-tg"
  }
}

# Target Group for HTTPS traffic
resource "aws_lb_target_group" "https" {
  name     = "${var.project_name}-${var.environment}-https-tg"
  port     = 443
  protocol = "TCP"
  vpc_id   = var.vpc_id

  health_check {
    enabled             = true
    healthy_threshold   = 2
    interval            = 30
    port                = "80"
    protocol            = "HTTP"
    timeout             = 6
    unhealthy_threshold = 2
  }

  tags = {
    Name = "${var.project_name}-${var.environment}-https-tg"
  }
}

# Target Group for Application Port
resource "aws_lb_target_group" "app" {
  name     = "${var.project_name}-${var.environment}-app-tg"
  port     = 8080
  protocol = "TCP"
  vpc_id   = var.vpc_id

  health_check {
    enabled             = true
    healthy_threshold   = 2
    interval            = 30
    port                = "80"
    protocol            = "HTTP"
    timeout             = 6
    unhealthy_threshold = 2
  }

  tags = {
    Name = "${var.project_name}-${var.environment}-app-tg"
  }
}

# Listener for HTTP traffic
resource "aws_lb_listener" "http" {
  load_balancer_arn = aws_lb.main.arn
  port              = "80"
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.http.arn
  }

  tags = {
    Name = "${var.project_name}-${var.environment}-http-listener"
  }
}

# Listener for HTTPS traffic
resource "aws_lb_listener" "https" {
  load_balancer_arn = aws_lb.main.arn
  port              = "443"
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.https.arn
  }

  tags = {
    Name = "${var.project_name}-${var.environment}-https-listener"
  }
}

# Listener for Application Port
resource "aws_lb_listener" "app" {
  load_balancer_arn = aws_lb.main.arn
  port              = "8080"
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.app.arn
  }

  tags = {
    Name = "${var.project_name}-${var.environment}-app-listener"
  }
}

# Target Group Attachments for HTTP
resource "aws_lb_target_group_attachment" "http" {
  count = length(var.ec2_instance_ids)

  target_group_arn = aws_lb_target_group.http.arn
  target_id        = var.ec2_instance_ids[count.index]
  port             = 80
}

# Target Group Attachments for HTTPS
resource "aws_lb_target_group_attachment" "https" {
  count = length(var.ec2_instance_ids)

  target_group_arn = aws_lb_target_group.https.arn
  target_id        = var.ec2_instance_ids[count.index]
  port             = 443
}

# Target Group Attachments for Application Port
resource "aws_lb_target_group_attachment" "app" {
  count = length(var.ec2_instance_ids)

  target_group_arn = aws_lb_target_group.app.arn
  target_id        = var.ec2_instance_ids[count.index]
  port             = 8080
}
