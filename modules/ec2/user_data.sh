#!/bin/bash
# Update system packages
apt-get update -y
apt-get upgrade -y

# Install Apache web server
apt-get install -y apache2
systemctl start apache2
systemctl enable apache2

# Create a simple index page
cat <<EOF > /var/www/html/index.html
<!DOCTYPE html>
<html>
<head>
    <title>${project_name} - ${environment}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .info { background: #f0f0f0; padding: 20px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Welcome to ${project_name}</h1>
        <div class="info">
            <h3>Instance Information</h3>
            <p><strong>Environment:</strong> ${environment}</p>
            <p><strong>Instance ID:</strong> <span id="instance-id">Loading...</span></p>
            <p><strong>Availability Zone:</strong> <span id="az">Loading...</span></p>
            <p><strong>Instance Type:</strong> <span id="instance-type">Loading...</span></p>
            <p><strong>Private IP:</strong> <span id="private-ip">Loading...</span></p>
            <p><strong>Operating System:</strong> Ubuntu 24.04 LTS</p>
        </div>
    </div>

    <script>
        // Fetch instance metadata
        fetch('http://***************/latest/meta-data/instance-id')
            .then(response => response.text())
            .then(data => document.getElementById('instance-id').textContent = data)
            .catch(error => document.getElementById('instance-id').textContent = 'N/A');

        fetch('http://***************/latest/meta-data/placement/availability-zone')
            .then(response => response.text())
            .then(data => document.getElementById('az').textContent = data)
            .catch(error => document.getElementById('az').textContent = 'N/A');

        fetch('http://***************/latest/meta-data/instance-type')
            .then(response => response.text())
            .then(data => document.getElementById('instance-type').textContent = data)
            .catch(error => document.getElementById('instance-type').textContent = 'N/A');

        fetch('http://***************/latest/meta-data/local-ipv4')
            .then(response => response.text())
            .then(data => document.getElementById('private-ip').textContent = data)
            .catch(error => document.getElementById('private-ip').textContent = 'N/A');
    </script>
</body>
</html>
EOF

# Install Docker
apt-get install -y docker.io
systemctl start docker
systemctl enable docker
usermod -a -G docker ubuntu

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Install AWS CLI v2
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
./aws/install
rm -rf awscliv2.zip aws/

# Install useful development tools
apt-get install -y \
    curl \
    wget \
    git \
    vim \
    htop \
    tree \
    jq \
    unzip \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release

# Install Node.js (LTS version)
curl -fsSL https://deb.nodesource.com/setup_lts.x | bash -
apt-get install -y nodejs

# Install Python 3 and pip (usually pre-installed on Ubuntu 24.04)
apt-get install -y python3 python3-pip python3-venv

# Create a welcome message for SSH users
cat <<EOF > /etc/motd

Welcome to ${project_name} - ${environment} Environment
========================================================

This Ubuntu 24.04 LTS instance is part of the Virtual Chef AI infrastructure.

Installed software:
- Apache2 web server
- Docker and Docker Compose
- AWS CLI v2
- Node.js (LTS)
- Python 3 with pip
- Development tools (git, vim, htop, etc.)

Instance details:
- Instance ID: \$(curl -s http://***************/latest/meta-data/instance-id 2>/dev/null || echo "N/A")
- Instance Type: \$(curl -s http://***************/latest/meta-data/instance-type 2>/dev/null || echo "N/A")
- Availability Zone: \$(curl -s http://***************/latest/meta-data/placement/availability-zone 2>/dev/null || echo "N/A")

========================================================

EOF

# Set proper permissions
chown -R ubuntu:ubuntu /home/<USER>
chmod 755 /var/www/html/index.html

# Log completion
echo "User data script completed at $(date)" >> /var/log/user-data.log
