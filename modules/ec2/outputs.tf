output "instance_ids" {
  description = "IDs of the EC2 instances"
  value       = concat(aws_instance.public[*].id, aws_instance.private[*].id)
}

output "public_instance_ids" {
  description = "IDs of the public EC2 instances"
  value       = aws_instance.public[*].id
}

output "private_instance_ids" {
  description = "IDs of the private EC2 instances"
  value       = aws_instance.private[*].id
}

output "public_ips" {
  description = "Public IP addresses of the EC2 instances"
  value       = aws_instance.public[*].public_ip
}

output "private_ips" {
  description = "Private IP addresses of all EC2 instances"
  value       = concat(aws_instance.public[*].private_ip, aws_instance.private[*].private_ip)
}

output "ec2_security_group_id" {
  description = "ID of the EC2 security group"
  value       = aws_security_group.ec2.id
}

output "launch_template_id" {
  description = "ID of the launch template"
  value       = aws_launch_template.main.id
}
