# AWS Configuration
aws_region = "us-west-2"

# Project Configuration
environment  = "dev"
project_name = "virtual-chef-ai"

# VPC Configuration
vpc_cidr             = "10.0.0.0/16"
availability_zones   = ["us-west-2a", "us-west-2b"]
public_subnet_cidrs  = ["10.0.1.0/24", "10.0.2.0/24"]
private_subnet_cidrs = ["10.0.10.0/24", "10.0.20.0/24"]

# EC2 Configuration
instance_type = "t3.micro"
key_pair_name = "your-key-pair-name"  # Optional: Leave empty if no SSH access needed

# RDS Configuration
db_instance_class = "db.t3.micro"
db_name           = "virtualchefai"
db_username       = "admin"
db_password       = "your-secure-password-here"  # Change this to a secure password
